<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <PropertyGroup>
    <Version>2025.07.10.01</Version>
    <!-- 主版本号 -->
    <FileVersion>2025.07.10.01</FileVersion>
    <!-- 文件版本 -->
    <AssemblyVersion>2025.07.10.01</AssemblyVersion>
    <!-- 程序集版本 -->
  </PropertyGroup>
  <PropertyGroup>
    <AllowedReferenceRelatedFileExtensions>
    .pdb;.xml
  </AllowedReferenceRelatedFileExtensions>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NLog" />
    <PackageReference Include="NLog.Extensions.Logging" />
    <PackageReference Include="System.IO.Ports" />
    <None Update="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>