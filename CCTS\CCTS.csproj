<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>Tecumseh</AssemblyName>
    <RootNamespace>CCTS</RootNamespace>
    <StartupObject>CCTS.Program</StartupObject>
    <ApplicationIcon>CCTS.ico</ApplicationIcon>
    <Win32Resource />
    <ApplicationManifest />
  </PropertyGroup>

  <!-- Debug配置 -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <!-- Release配置 -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <Version>2025.07.10.01</Version>
    <!-- 主版本号 -->
    <FileVersion>2025.07.10.01</FileVersion>
    <!-- 文件版本 -->
    <AssemblyVersion>2025.07.10.01</AssemblyVersion>
    <!-- 程序集版本 -->
  </PropertyGroup>
  <PropertyGroup>
    <AllowedReferenceRelatedFileExtensions>
    .pdb;.xml
  </AllowedReferenceRelatedFileExtensions>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NLog" />
    <PackageReference Include="NLog.Extensions.Logging" />
    <PackageReference Include="System.Configuration.ConfigurationManager" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="System.IO.Ports" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="CCTS.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Update="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>