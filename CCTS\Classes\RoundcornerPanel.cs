using System;
using System.Windows.Forms;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.Windows.Forms.VisualStyles;
using System.Reflection;


namespace CCTS
{
    public class RoundcornerPanel : Panel
    {
        protected override void OnPaint(PaintEventArgs e)
        {
            GraphicsPath path = new GraphicsPath();
            int radius = 30; // 圆角半径
            Rectangle rect = new Rectangle(0, 0, Width, Height);

            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseFigure();

            this.Region = new Region(path);
            base.OnPaint(e);
        }

        public static implicit operator RoundcornerPanel(DoubleBufferedDataGridView v)
        {
            throw new NotImplementedException();
        }
    }
    public class Roundcornerbtn : Button
    {
        protected override void OnPaint(PaintEventArgs e)
        {
            GraphicsPath path = new GraphicsPath();
            int radius = 30; // 圆角半径
            Rectangle rect = new Rectangle(0, 0, Width, Height);

            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseFigure();

            this.Region = new Region(path);
            base.OnPaint(e);
        }
    }

    public class CenteredTextBox : RichTextBox
    {
        public CenteredTextBox()
        {
            //控件将自行处理绘制逻辑（通过重写OnPaint方法）;启用双缓冲技术，减少绘制时的闪烁现象
            SetStyle(ControlStyles.UserPaint | ControlStyles.OptimizedDoubleBuffer, true);
            this.Multiline = true;
        }
        // 设置光标高度与控件等高
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.Height = this.Height; // 使光标高度与控件高度相同
                return cp;
            }
        }
        protected override void OnKeyDown(KeyEventArgs e)
        {
            // 拦截Enter键
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;  // 阻止换行
                return;
            }
            base.OnKeyDown(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            // 清除背景
            e.Graphics.Clear(this.BackColor);

            // 计算文本尺寸
            SizeF textSize = e.Graphics.MeasureString(this.Text, this.Font, this.ClientSize.Width);

            // 计算垂直居中位置
            float yPos = (this.ClientSize.Height - textSize.Height) / 2;

            // 计算水平居中位置
            float xPos = (this.ClientSize.Width - textSize.Width) / 2;

            // 绘制文本（同时实现水平和垂直居中）
            e.Graphics.DrawString(this.Text, this.Font, new SolidBrush(this.ForeColor),
                                new PointF(xPos, yPos));

        }

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
            this.Invalidate(); // 触发重绘
        }


    }
    public class DoubleBufferedDataGridView : DataGridView
    {
        public DoubleBufferedDataGridView()
        {
            // 使用反射启用双缓冲
            typeof(DataGridView).InvokeMember("DoubleBuffered",
                BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.SetProperty,
                null, this, new object[] { true });
        }
    }
      public class SingleDataGridView : DataGridView
    {
        public SingleDataGridView()
        {
            //设置grid样式
            this.AllowUserToAddRows = false;
            this.AllowUserToOrderColumns = false;
            this.AllowUserToDeleteRows = false;
            this.AllowUserToResizeRows = false;
            this.AllowUserToResizeColumns = false;
            this.RowHeadersVisible = false;
            this.BorderStyle = BorderStyle.None;
            this.RowHeadersVisible = false;
            this.ColumnHeadersVisible = false;

            this.BackgroundColor = Color.FromArgb(11, 48, 65);
            this.GridColor = Color.FromArgb(11, 48, 65);
            this.Font = new Font(this.Font.FontFamily, 10);
            this.Height = 70;               
            this.DefaultCellStyle.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;   
            //添加列   
            DataGridViewColumn customColumn;
            customColumn = new DataGridViewTextBoxColumn();  
            this.Columns.Add(customColumn);
            
            //添加行      
            int rowIndex = this.Rows.Add();    
            this.Rows[rowIndex].Cells[0].Style.BackColor = Color.White;
            this.Rows[rowIndex].Cells[0].Style.ForeColor = Color.Black;
            this.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(11, 48, 65);                    
        }
  
    }    


}