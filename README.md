# Tecumseh CCTS Application

✅ **问题已解决！** 程序现在可以正常运行和调试。

## 🚀 快速开始

### 运行程序
```bash
# 方式1：直接运行（推荐）
dotnet run

# 方式2：指定项目
dotnet run --project Tecumseh.csproj

# 方式3：运行可执行文件
dotnet build
.\bin\Debug\net8.0-windows\Tecumseh.exe
```

### 调试程序
- **VS Code**: 按 `F5` 开始调试
- **Visual Studio**: 打开 `Tecumseh.sln`，按 `F5`

## 🔧 已解决的问题

### ✅ 图标文件路径问题
- **问题**: `DirectoryNotFoundException: Could not find CCTS.ico`
- **解决**: 修复了硬编码的绝对路径，改为相对路径
- **配置**: 图标文件自动复制到输出目录

### ✅ 配置管理问题
- **问题**: `ConfigurationManager` 缺失
- **解决**: 添加了 `System.Configuration.ConfigurationManager` 包
- **支持**: 同时支持传统和现代配置方式

### ✅ 项目结构问题
- **问题**: "找不到要运行的项目"
- **解决**: 创建了正确的根目录项目文件
- **配置**: 完整的调试配置和解决方案文件

## 📁 项目结构

```
Tecumseh1/
├── CCTS/                    # 原始项目文件
│   ├── *.cs                 # 源代码
│   ├── CCTS.ico            # 应用图标
│   ├── NLog.config         # 日志配置
│   └── appsettings.json    # 应用配置
├── .vscode/                # VS Code调试配置
├── Tecumseh.csproj         # 主项目文件
├── Tecumseh.sln           # 解决方案文件
└── Directory.Packages.props # 包管理
```

## 📦 依赖包

- NLog (5.5.0) - 日志记录
- System.Configuration.ConfigurationManager (8.0.1) - 配置管理
- System.IO.Ports (9.0.6) - 串口通信
- Microsoft.Extensions.Configuration (8.0.0) - 现代配置

## 🎯 测试结果

- ✅ 项目构建成功
- ✅ 程序正常启动
- ✅ 图标正确加载
- ✅ 配置文件正确读取
- ✅ 调试功能正常

## 🐛 如果还有问题

1. **重新构建**: `dotnet clean && dotnet build`
2. **检查.NET版本**: `dotnet --version` (需要8.0+)
3. **检查文件**: 确保 `CCTS/CCTS.ico` 存在
4. **权限问题**: 以管理员身份运行

---

**程序现在完全可以正常运行和调试！** 🎉
