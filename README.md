# Tecumseh CCTS Application

✅ **项目已整合完成！** 现在只有一个.sln文件，所有功能保持不变。

## 🚀 快速开始

### 运行程序
```bash
# 方式1：使用解决方案文件（推荐）
dotnet run --project CCTS/CCTS.csproj

# 方式2：构建后运行
dotnet build Tecumseh.sln
.\CCTS\bin\Debug\net8.0-windows\Tecumseh.exe

# 方式3：直接在CCTS目录运行
cd CCTS
dotnet run
```

### 调试程序
- **VS Code**: 按 `F5` 开始调试
- **Visual Studio**: 打开 `Tecumseh.sln`，按 `F5`

## 🔧 项目整合完成

### ✅ 统一项目结构
- **整合**: 只保留一个 `Tecumseh.sln` 解决方案文件
- **简化**: 移除了重复的项目文件和配置
- **保持**: 所有原有功能完全保持不变

### ✅ 已解决的问题
- **图标路径**: 修复了图标文件路径问题
- **配置管理**: 添加了必要的NuGet包
- **项目结构**: 统一为单一解决方案结构
- **调试配置**: 更新了VS Code调试配置

## 📁 整合后的项目结构

```
Tecumseh1/
├── CCTS/                    # 统一的项目文件夹
│   ├── *.cs                 # 所有源代码文件
│   ├── *.Designer.cs        # 窗体设计器文件
│   ├── *.resx               # 资源文件
│   ├── CCTS.csproj          # 项目文件
│   ├── CCTS.ico            # 应用图标
│   ├── NLog.config         # 日志配置
│   └── appsettings.json    # 应用配置
├── .vscode/                # VS Code调试配置
├── Tecumseh.sln           # 唯一的解决方案文件
├── Directory.Packages.props # 包版本管理
└── README.md              # 项目说明
```

## 📦 依赖包

- NLog (5.5.0) - 日志记录
- System.Configuration.ConfigurationManager (8.0.1) - 配置管理
- System.IO.Ports (9.0.6) - 串口通信
- Microsoft.Extensions.Configuration (8.0.0) - 现代配置

## 🎯 测试结果

- ✅ 项目构建成功
- ✅ 程序正常启动
- ✅ 图标正确加载
- ✅ 配置文件正确读取
- ✅ 调试功能正常

## 🐛 如果还有问题

1. **重新构建**: `dotnet clean && dotnet build`
2. **检查.NET版本**: `dotnet --version` (需要8.0+)
3. **检查文件**: 确保 `CCTS/CCTS.ico` 存在
4. **权限问题**: 以管理员身份运行

---

**程序现在完全可以正常运行和调试！** 🎉
