# Tecumseh CCTS Application

✅ **项目已完全配置！** 支持标准的Visual Studio C#窗体开发，完整的Debug和Release模式。

## 🚀 快速开始

### Debug模式运行
```bash
# 方式1：Debug模式（推荐）
dotnet run --project CCTS/CCTS.csproj --configuration Debug

# 方式2：使用批处理脚本
.\run.bat

# 方式3：直接运行Debug可执行文件
.\CCTS\bin\Debug\net8.0-windows\Tecumseh.exe
```

### Release模式运行
```bash
# 方式1：Release模式
dotnet run --project CCTS/CCTS.csproj --configuration Release

# 方式2：使用批处理脚本
.\run-release.bat

# 方式3：直接运行Release可执行文件
.\CCTS\bin\Release\net8.0-windows\Tecumseh.exe
```

### 构建项目
```bash
# Debug构建
dotnet build Tecumseh.sln --configuration Debug

# Release构建
dotnet build Tecumseh.sln --configuration Release

# 使用交互式构建脚本
.\build.bat
```

### 调试程序
- **VS Code**:
  - Debug模式: 选择 "Launch Tecumseh (Debug)" 配置，按 `F5`
  - Release模式: 选择 "Launch Tecumseh (Release)" 配置
- **Visual Studio**:
  - 打开 `Tecumseh.sln`
  - 选择 Debug 或 Release 配置
  - 按 `F5` 开始调试

## 🔧 完整的Visual Studio C#窗体开发支持

### ✅ 标准配置
- **Debug配置**: 完整调试符号，未优化，包含DEBUG和TRACE常量
- **Release配置**: 优化代码，仅PDB符号，包含TRACE常量
- **多平台支持**: Any CPU, x64, x86 配置
- **应用图标**: 正确配置的应用程序图标

### ✅ 开发功能
- **断点调试**: 完整的断点和单步调试支持
- **变量监视**: 实时变量值监视
- **调用堆栈**: 完整的调用堆栈跟踪
- **异常处理**: 详细的异常信息和堆栈跟踪

## 📁 整合后的项目结构

```
Tecumseh1/
├── CCTS/                    # 统一的项目文件夹
│   ├── *.cs                 # 所有源代码文件
│   ├── *.Designer.cs        # 窗体设计器文件
│   ├── *.resx               # 资源文件
│   ├── CCTS.csproj          # 项目文件
│   ├── CCTS.ico            # 应用图标
│   ├── NLog.config         # 日志配置
│   └── appsettings.json    # 应用配置
├── .vscode/                # VS Code调试配置
├── Tecumseh.sln           # 唯一的解决方案文件
├── Directory.Packages.props # 包版本管理
└── README.md              # 项目说明
```

## 📦 依赖包

- NLog (5.5.0) - 日志记录
- System.Configuration.ConfigurationManager (8.0.1) - 配置管理
- System.IO.Ports (9.0.6) - 串口通信
- Microsoft.Extensions.Configuration (8.0.0) - 现代配置

## 🎯 测试结果

- ✅ 项目构建成功
- ✅ 程序正常启动
- ✅ 图标正确加载
- ✅ 配置文件正确读取
- ✅ 调试功能正常

## 🐛 如果还有问题

1. **重新构建**: `dotnet clean && dotnet build`
2. **检查.NET版本**: `dotnet --version` (需要8.0+)
3. **检查文件**: 确保 `CCTS/CCTS.ico` 存在
4. **权限问题**: 以管理员身份运行

---

**程序现在完全可以正常运行和调试！** 🎉
