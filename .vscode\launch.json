{"version": "0.2.0", "configurations": [{"name": "Launch Tecumseh", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0-windows/Tecumseh.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false}, {"name": "Launch CCTS (Alternative)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-ccts", "program": "${workspaceFolder}/CCTS/bin/Debug/net8.0-windows/CCTS.dll", "args": [], "cwd": "${workspaceFolder}/CCTS", "console": "internalConsole", "stopAtEntry": false}]}