{"version": "0.2.0", "configurations": [{"name": "Launch Tecumseh (Debug)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-debug", "program": "${workspaceFolder}/bin/Debug/net8.0-windows/Tecumseh.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "justMyCode": true, "enableStepFiltering": true}, {"name": "Launch Tecumseh (Release)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-release", "program": "${workspaceFolder}/bin/Release/net8.0-windows/Tecumseh.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "justMyCode": false, "enableStepFiltering": false}]}