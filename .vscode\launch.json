{"version": "0.2.0", "configurations": [{"name": "Launch Tecumseh (Debug)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-debug", "program": "${workspaceFolder}/CCTS/bin/Debug/net8.0-windows/Tecumseh.dll", "args": [], "cwd": "${workspaceFolder}/CCTS", "console": "internalConsole", "stopAtEntry": false, "justMyCode": true, "enableStepFiltering": true}, {"name": "Launch Tecumseh (Release)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-release", "program": "${workspaceFolder}/CCTS/bin/Release/net8.0-windows/Tecumseh.dll", "args": [], "cwd": "${workspaceFolder}/CCTS", "console": "internalConsole", "stopAtEntry": false, "justMyCode": false, "enableStepFiltering": false}]}