@echo off
echo ========================================
echo Tecumseh CCTS Build Script
echo ========================================
echo.

:menu
echo Please select build configuration:
echo 1. Debug
echo 2. Release
echo 3. Both (Debug + Release)
echo 4. Clean
echo 5. Rebuild (Clean + Build Debug)
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto debug
if "%choice%"=="2" goto release
if "%choice%"=="3" goto both
if "%choice%"=="4" goto clean
if "%choice%"=="5" goto rebuild
if "%choice%"=="6" goto exit
echo Invalid choice. Please try again.
goto menu

:debug
echo.
echo Building Debug configuration...
dotnet build Tecumseh.sln --configuration Debug
if %ERRORLEVEL% EQU 0 (
    echo ✓ Debug build successful!
) else (
    echo ✗ Debug build failed!
)
goto end

:release
echo.
echo Building Release configuration...
dotnet build Tecumseh.sln --configuration Release
if %ERRORLEVEL% EQU 0 (
    echo ✓ Release build successful!
) else (
    echo ✗ Release build failed!
)
goto end

:both
echo.
echo Building both Debug and Release configurations...
dotnet build Tecumseh.sln --configuration Debug
if %ERRORLEVEL% EQU 0 (
    echo ✓ Debug build successful!
    dotnet build Tecumseh.sln --configuration Release
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Release build successful!
        echo ✓ All builds completed successfully!
    ) else (
        echo ✗ Release build failed!
    )
) else (
    echo ✗ Debug build failed!
)
goto end

:clean
echo.
echo Cleaning solution...
dotnet clean Tecumseh.sln
echo ✓ Clean completed!
goto end

:rebuild
echo.
echo Rebuilding (Clean + Debug)...
dotnet clean Tecumseh.sln
dotnet build Tecumseh.sln --configuration Debug
if %ERRORLEVEL% EQU 0 (
    echo ✓ Rebuild successful!
) else (
    echo ✗ Rebuild failed!
)
goto end

:end
echo.
pause
goto menu

:exit
echo Goodbye!
