{"format": 1, "restore": {"C:\\work\\C#\\Tecumseh1\\CCTS\\CCTS.csproj": {}}, "projects": {"C:\\work\\C#\\Tecumseh1\\CCTS\\CCTS.csproj": {"version": "2025.7.10.1", "restore": {"projectUniqueName": "C:\\work\\C#\\Tecumseh1\\CCTS\\CCTS.csproj", "projectName": "CCTS", "projectPath": "C:\\work\\C#\\Tecumseh1\\CCTS\\CCTS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\C#\\Tecumseh1\\CCTS\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )", "versionCentrallyManaged": true}, "NLog": {"target": "Package", "version": "[5.5.0, )", "versionCentrallyManaged": true}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )", "versionCentrallyManaged": true}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[8.0.1, )", "versionCentrallyManaged": true}, "System.IO.Ports": {"target": "Package", "version": "[9.0.6, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "NLog": "5.5.0", "NLog.Extensions.Logging": "5.5.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.IO.Ports": "9.0.6"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.108/PortableRuntimeIdentifierGraph.json"}}}}}