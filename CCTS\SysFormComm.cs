using System.IO.Ports;

namespace CCTS;

public partial class SysFormComm : Form
{
    private MainForm mainForm;
    public bool bLink = false;
    
    public SysFormComm(MainForm mainForm)
    {
        InitializeComponent();
        this.mainForm = mainForm;
        //串口号
        foreach (string portName in SysConfig.portNames)
        {
            this.combo_comm.Items.Add(portName);
            //mainForm.combo_Com.Items.Add(portName);
        }

        //波特率
        combo_bps.Items.Add("9600");
        combo_bps.Items.Add("19200");
        combo_bps.Items.Add("38400");
        combo_bps.Items.Add("76800");
        combo_bps.Items.Add("115200");
        combo_bps.Items.Add("230400");
        combo_bps.Items.Add("460800");
        combo_bps.SelectedItem = "" + SysConfig.iBps;
        //校验
        combo_parity.Items.Add("None");//无校验
        combo_parity.Items.Add("Odd");//奇校验
        combo_parity.Items.Add("Even");//偶校验
        combo_parity.SelectedIndex = SysConfig.iParity;
        //数据位
        combo_databit.Items.Add("6");
        combo_databit.Items.Add("7");
        combo_databit.Items.Add("8");
        combo_databit.SelectedItem = "" + SysConfig.iDatabit;
        //停止位
        combo_stopbit.Items.Add("1");
        combo_stopbit.Items.Add("2");
        combo_stopbit.SelectedIndex = SysConfig.iStopbit - 1;

        if (SysConfig.strPort != null)
        {
            combo_comm.SelectedItem = SysConfig.strPort;
            mainForm.combo_Com.SelectedItem = SysConfig.strPort;

        }
        else if (combo_comm.Items.Count > 0)
        {
            combo_comm.SelectedIndex = combo_comm.Items.Count - 1;
            mainForm.combo_Com.SelectedIndex = this.combo_comm.Items.Count - 1;
        }
    }



    private void btn_connect_Click(object sender, EventArgs e)
    {
        save();
        mainForm.serialPort.BaudRate = SysConfig.iBps;            
        mainForm.serialPort.Parity = (Parity)SysConfig.iParity;           
        mainForm.serialPort.DataBits = SysConfig.iDatabit;            
        mainForm.serialPort.StopBits = (StopBits)SysConfig.iStopbit;          
        mainForm.serialPort.PortName = SysConfig.strPort;
        bLink = true;
        this.Close();
    }
    private void btn_save_Click(object sender, EventArgs e)
    {
        save();
        bLink = false;
        this.Close();
    }

    private void save()
    {
        SysConfig.iBps = int.Parse(combo_bps.SelectedItem?.ToString() ?? "9600");
        SysConfig.iParity = combo_parity.SelectedIndex;
        SysConfig.iDatabit = int.Parse(combo_databit.SelectedItem?.ToString() ?? "8");
        SysConfig.iStopbit = combo_stopbit.SelectedIndex + 1;

        if (combo_comm.SelectedIndex != -1)
            SysConfig.strPort = combo_comm.SelectedItem?.ToString();

        SysConfig.saveComm();
    }
 
}
