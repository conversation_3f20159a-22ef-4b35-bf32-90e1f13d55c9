{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Tecumseh.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-ccts", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/CCTS/CCTS.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Tecumseh.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/Tecumseh.csproj"], "problemMatcher": "$msCompile"}]}