{"version": "2.0.0", "tasks": [{"label": "build-debug", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Tecumseh.sln", "--configuration", "Debug", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": "$msCompile"}, {"label": "build-release", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Tecumseh.sln", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "group": "build", "problemMatcher": "$msCompile"}, {"label": "build", "dependsOn": "build-debug"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/Tecumseh.sln"], "group": "build", "problemMatcher": "$msCompile"}, {"label": "rebuild", "dependsOrder": "sequence", "dependsOn": ["clean", "build-debug"], "group": "build"}, {"label": "publish-debug", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Tecumseh.sln", "--configuration", "Debug", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish-release", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Tecumseh.sln", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run"], "problemMatcher": "$msCompile"}]}