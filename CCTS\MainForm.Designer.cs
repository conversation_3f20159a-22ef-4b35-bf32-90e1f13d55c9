namespace CCTS;

partial class MainForm
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code
    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// 2025-06-05
    /// </summary>
    private void InitializeComponent()
    {
        this.components = new System.ComponentModel.Container();
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(2000, 1000);
        this.Text = "CCTS 2025071001";
        this.Icon = new System.Drawing.Icon(@"C:\work\C#\Tecumseh\CCTS\CCTS.ico");
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(78, 149, 217);
        //this.MainForm_Closed += new System.Windows.Forms.FormClosedEventHandler(this.MainForm_Closed);
        //
        //label1
        //
        this.label1 = new Label();
        this.label1.Text = "HOME";
        this.label1.Size = new Size(240, 100);
        this.label1.Anchor = AnchorStyles.None;
        this.label1.Location = new Point((this.ClientSize.Width - label1.Width) / 2, 60);
        this.label1.Font = new Font(label1.Font.FontFamily, 24, FontStyle.Bold);
        this.label1.ForeColor = Color.White;
        this.Controls.Add(label1);
        //
        //panel1
        //
        this.panel1 = new RoundcornerPanel();
        this.panel1.Size = new Size(460, 500);
        this.panel1.TabIndex = 0;
        this.panel1.Location = new Point(120, 220);
        this.panel1.BackColor = Color.FromArgb(11, 48, 65);
        this.panel1.BorderStyle = BorderStyle.None;
        this.Controls.Add(panel1);
        //
        //btn_comm
        //
        // this.btn_comm = new CCTS.Roundcornerbtn();
        // this.btn_comm.Text = "COMM CONFIG";
        // this.btn_comm.Size = new Size(350, 40);
        // this.btn_comm.Location = new Point((this.panel1.Width - this.btn_comm.Width) / 2, 40);
        // this.btn_comm.ForeColor = Color.White;
        // this.btn_comm.FlatStyle = FlatStyle.Flat;
        // this.btn_comm.FlatAppearance.BorderSize = 0;
        // this.btn_comm.Font = new Font(btn_comm.Font.FontFamily, 12, FontStyle.Bold);
        // this.btn_comm.Click += new EventHandler(this.btn_comm_Click);
        // this.panel1.Controls.Add(btn_comm);
        //
        //label2
        //
        this.label2 = new Label();
        this.label2.Text = "COMM CONFIG";
        this.label2.Size = new Size(240, 40);
        this.label2.Location = new Point((panel1.ClientSize.Width - label2.Width) / 2, 40);
        this.label2.ForeColor = Color.White;
        this.label2.BorderStyle = BorderStyle.None;
        this.label2.Font = new Font(label2.Font.FontFamily, 12, FontStyle.Bold);
        this.label2.Click += new EventHandler(this.label2_Click);
        panel1.Controls.Add(label2);
        //
        //lab_com
        //
        this.lab_com = new Label();
        this.lab_com.Text = "COM Config:";
        this.lab_com.Size = new Size(200, 40);
        this.lab_com.Location = new Point(15, label2.Height + 100);
        this.lab_com.ForeColor = Color.White;
        this.lab_com.Font = new Font(lab_com.Font.FontFamily, 12);
        panel1.Controls.Add(lab_com);
        //
        //combo_Com
        //
        this.combo_Com = new ComboBox();
        this.combo_Com.Size = new Size(200, 100);      
        this.combo_Com.Location = new Point(lab_com.Width + 15, label2.Height + 100);
        this.combo_Com.FlatStyle = FlatStyle.Standard;
        this.combo_Com.DropDownStyle = ComboBoxStyle.DropDown;
        this.combo_Com.BackColor = Color.White;
        this.combo_Com.ForeColor = Color.Black;
        this.combo_Com.Font = new Font(this.combo_Com.Font.FontFamily, 10);
        panel1.Controls.Add(combo_Com);
        //
        //label3
        //
        this.label3 = new Label();
        this.label3.Text = "Inverter Model:";
        this.label3.Size = new Size(240, 40);
        this.label3.Location = new Point(15, label2.Height + lab_com.Height + 150);
        this.label3.ForeColor = Color.White;
        this.label3.Font = new Font(label3.Font.FontFamily, 12);
        panel1.Controls.Add(label3);
        //
        //lab_inverterModel
        //
        this.lab_inverterModel = new Label();
        this.lab_inverterModel.Text = "VTC_250W";
        this.lab_inverterModel.Size = new Size(220, 40);
        this.lab_inverterModel.Location = new Point(label3.Width + 10, label2.Height + lab_com.Height + 150);
        this.lab_inverterModel.ForeColor = Color.White;
        this.lab_inverterModel.Font = new Font(lab_inverterModel.Font.FontFamily, 12);
        panel1.Controls.Add(lab_inverterModel);
        //
        //label4
        //
        this.label4 = new Label();
        this.label4.Text = "Software Version:";
        this.label4.Size = new Size(250, 40);
        this.label4.Location = new Point(15, label2.Height + lab_com.Height + label3.Height + 170);
        this.label4.ForeColor = Color.White;
        this.label4.Font = new Font(label4.Font.FontFamily, 12);
        panel1.Controls.Add(label4);
        //
        //lab_softwareVersion
        //
        this.lab_softwareVersion = new Label();
        this.lab_softwareVersion.Text = "vtc250w_v01";
        this.lab_softwareVersion.Size = new Size(220, 40);
        this.lab_softwareVersion.Location = new Point(label4.Width + 10, label2.Height + lab_com.Height + label3.Height + 170);
        this.lab_softwareVersion.ForeColor = Color.White;
        this.lab_softwareVersion.Font = new Font(lab_softwareVersion.Font.FontFamily, 12);
        panel1.Controls.Add(lab_softwareVersion);
        //
        //btn_connect
        //
        this.btn_connect = new Roundcornerbtn();
        this.btn_connect.Text = "Connect";
        this.btn_connect.Size = new Size(180, 80);
        this.btn_connect.Location = new Point((panel1.ClientSize.Width - btn_connect.Width) / 2,
                                            label2.Height + lab_com.Height + label3.Height + label4.Height + 210);
        this.btn_connect.ForeColor = Color.White;
        this.btn_connect.Font = new Font(btn_connect.Font.FontFamily, 12);
        this.btn_connect.BackColor = Color.FromArgb(192, 79, 21);
        this.btn_connect.FlatStyle = FlatStyle.Flat;
        this.btn_connect.FlatAppearance.BorderSize = 0;
        this.btn_connect.Click += new EventHandler(this.btn_connect_Click);
        panel1.Controls.Add(btn_connect);
        //
        //按钮parameters configuration
        //
        this.btn_Configuration = new Roundcornerbtn();
        this.btn_Configuration.Text = "PARAMETERS\nCONFIGURATION";
        this.btn_Configuration.TextAlign = ContentAlignment.MiddleCenter;
        this.btn_Configuration.Size = new Size(400, 400);
        this.btn_Configuration.Location = new Point((panel1.Width + 155), 280);
        this.btn_Configuration.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_Configuration.ForeColor = Color.White;
        this.btn_Configuration.FlatStyle = FlatStyle.Flat;
        this.btn_Configuration.FlatAppearance.BorderSize = 0;
        this.btn_Configuration.Font = new Font(btn_Configuration.Font.FontFamily, 12, FontStyle.Bold);
        this.btn_Configuration.Click += new EventHandler(this.btn_Configuration_Click);
        this.btn_Configuration.Visible = false;
        this.Controls.Add(btn_Configuration);
        //
        //按钮parameters log
        //
        this.btn_Log = new Roundcornerbtn();
        this.btn_Log.Text = "PARAMETERS\nLOG";
        this.btn_Log.TextAlign = ContentAlignment.MiddleCenter;
        this.btn_Log.Size = new Size(400, 400);
        this.btn_Log.Location = new Point((panel1.Width + btn_Configuration.Width + 190), 280);
        this.btn_Log.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_Log.ForeColor = Color.White;
        this.btn_Log.FlatStyle = FlatStyle.Flat;
        this.btn_Log.FlatAppearance.BorderSize = 0;
        this.btn_Log.Font = new Font(btn_Log.Font.FontFamily, 12, FontStyle.Bold);
        this.btn_Log.Click += new EventHandler(this.btn_Log_Click); ;
        this.btn_Log.Visible = false;
        this.Controls.Add(btn_Log);
        //
        //按钮TAL configuration
        //
        this.btn_TAL = new Roundcornerbtn();
        this.btn_TAL.Text = "TAL\nCONFIGURATION";
        this.btn_TAL.TextAlign = ContentAlignment.MiddleCenter;
        this.btn_TAL.Size = new Size(400, 400);
        this.btn_TAL.Location = new Point((panel1.Width + btn_Configuration.Width + btn_Log.Width + 225), 280);
        this.btn_TAL.BackColor = Color.FromArgb(11, 48, 65);
        this.btn_TAL.ForeColor = Color.White;
        this.btn_TAL.FlatStyle = FlatStyle.Flat;
        this.btn_TAL.FlatAppearance.BorderSize = 0;
        this.btn_TAL.Font = new Font(btn_TAL.Font.FontFamily, 12, FontStyle.Bold);
        this.btn_TAL.Click += new EventHandler(this.btn_TAL_Click); 
        this.btn_TAL.Visible = false;
        this.Controls.Add(btn_TAL);
    }

    #endregion     
    private System.Windows.Forms.Label label1, label2, lab_com, label3, lab_inverterModel, label4, lab_softwareVersion;
    private System.Windows.Forms.Button btn_connect, btn_Configuration, btn_Log, btn_TAL;// btn_comm;
    private System.Windows.Forms.Panel panel1;
    public System.Windows.Forms.ComboBox combo_Com;
}
