<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Tecumseh</AssemblyName>
    <RootNamespace>CCTS</RootNamespace>
    <StartupObject>CCTS.Program</StartupObject>
    <ApplicationIcon>CCTS\CCTS.ico</ApplicationIcon>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
  </PropertyGroup>
  
  <!-- Debug配置 -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>false</Optimize>
  </PropertyGroup>
  
  <!-- Release配置 -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="NLog" />
    <PackageReference Include="NLog.Extensions.Logging" />
    <PackageReference Include="System.Configuration.ConfigurationManager" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="System.IO.Ports" />
  </ItemGroup>
  
  <ItemGroup>
    <Compile Include="CCTS\*.cs" />
    <EmbeddedResource Include="CCTS\*.resx" />
    <Content Include="CCTS\CCTS.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Update="CCTS\NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="CCTS\appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
