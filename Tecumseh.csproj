<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>Tecumseh</AssemblyName>
    <RootNamespace>CCTS</RootNamespace>
    <StartupObject>CCTS.Program</StartupObject>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
  </PropertyGroup>
  <PropertyGroup>
    <Version>2025.07.10.01</Version>
    <FileVersion>2025.07.10.01</FileVersion>
    <AssemblyVersion>2025.07.10.01</AssemblyVersion>
  </PropertyGroup>
  <PropertyGroup>
    <AllowedReferenceRelatedFileExtensions>
    .pdb;.xml
  </AllowedReferenceRelatedFileExtensions>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="NLog" />
    <PackageReference Include="NLog.Extensions.Logging" />
    <PackageReference Include="System.Configuration.ConfigurationManager" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="System.IO.Ports" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CCTS\*.cs" Exclude="CCTS\obj\**\*.cs" />
    <EmbeddedResource Include="CCTS\*.resx" />
    <None Update="CCTS\NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="CCTS\appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="CCTS\CCTS.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>