<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Tecumseh</AssemblyName>
    <RootNamespace>CCTS</RootNamespace>
    <StartupObject>CCTS.Program</StartupObject>
    <ApplicationIcon>CCTS\Resources\CCTS.ico</ApplicationIcon>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
  </PropertyGroup>
  
  <!-- Debug配置 -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <!-- Release配置 -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <WarningLevel>4</WarningLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="NLog" />
    <PackageReference Include="NLog.Extensions.Logging" />
    <PackageReference Include="System.Configuration.ConfigurationManager" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="System.IO.Ports" />
  </ItemGroup>
  
  <ItemGroup>
    <Compile Include="CCTS\Program.cs" />
    <Compile Include="CCTS\Forms\*.cs" />
    <Compile Include="CCTS\Classes\*.cs" />
    <EmbeddedResource Include="CCTS\Forms\*.resx" />
    <Content Include="CCTS\Resources\CCTS.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Update="CCTS\Config\NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="CCTS\Config\appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
