C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.csproj.AssemblyReference.cache
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.GeneratedMSBuildEditorConfig.editorconfig
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.AssemblyInfoInputs.cache
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.AssemblyInfo.cs
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.csproj.CoreCompileInputs.cache
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\CCTS\Resources\CCTS.ico
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\CCTS\Config\appsettings.json
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\CCTS\Config\NLog.config
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Tecumseh.exe
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Tecumseh.deps.json
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Tecumseh.runtimeconfig.json
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Tecumseh.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Tecumseh.pdb
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\NLog.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\NLog.Extensions.Logging.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\System.Configuration.ConfigurationManager.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\System.Diagnostics.EventLog.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\System.IO.Ports.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
C:\work\C#\Tecumseh1\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.csproj.Up2Date
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.dll
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\refint\Tecumseh.dll
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.pdb
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\Tecumseh.genruntimeconfig.cache
C:\work\C#\Tecumseh1\obj\Debug\net8.0-windows\ref\Tecumseh.dll
