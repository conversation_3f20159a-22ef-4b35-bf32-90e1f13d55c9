@echo off
echo ========================================
echo Tecumseh CCTS Debug Test
echo ========================================
echo.

echo 1. Checking Debug build...
if exist "bin\Debug\net8.0-windows\Tecumseh.exe" (
    echo ✓ Tecumseh.exe found
) else (
    echo ✗ Tecumseh.exe NOT found
    goto end
)

if exist "bin\Debug\net8.0-windows\Tecumseh.pdb" (
    echo ✓ Tecumseh.pdb found (Debug symbols)
) else (
    echo ✗ Tecumseh.pdb NOT found
    goto end
)

echo.
echo 2. Testing application startup...
echo Starting application for 3 seconds...
start /B "" "bin\Debug\net8.0-windows\Tecumseh.exe"
timeout /t 3 /nobreak >nul

echo.
echo 3. Stopping application...
taskkill /f /im Tecumseh.exe >nul 2>&1

echo.
echo ✓ Debug test completed successfully!
echo.
echo You can now:
echo - Use 'dotnet run' to start the application
echo - Use F5 in VS Code for debugging
echo - Use F5 in Visual Studio for debugging

:end
echo.
pause
